#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房间分割演示脚本
展示如何使用改进的房间分割器进行家居地图房间分割

功能特点:
1. 基于家具检测结果推断完整房间区域
2. 使用门作为房间分界线
3. 自动识别unknown房间类型
4. 支持批量处理

作者: AI Assistant
日期: 2025-06-18
"""

import os
import sys
import argparse
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_room_segmenter import AdvancedRoomSegmenter

def process_single_image(segmenter, image_path, label_path, output_dir):
    """处理单张图像"""
    try:
        result = segmenter.process_image(image_path, label_path, output_dir)
        
        print(f"\n✅ 成功处理: {os.path.basename(image_path)}")
        print(f"📊 房间统计:")
        for room_name, stats in result["room_statistics"].items():
            if stats["pixel_count"] > 0:
                print(f"   - {room_name}: {stats['pixel_count']} 像素 ({stats['percentage']:.1f}%)")
        
        return result
    except Exception as e:
        print(f"❌ 处理失败 {os.path.basename(image_path)}: {e}")
        return None

def process_directory(segmenter, input_dir, output_dir):
    """批量处理目录中的图像"""
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 支持的图像格式
    image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    
    # 查找所有图像文件
    image_files = []
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        print(f"❌ 在目录 {input_dir} 中未找到图像文件")
        return []
    
    print(f"📁 找到 {len(image_files)} 个图像文件")
    
    results = []
    for image_file in image_files:
        # 查找对应的标签文件
        label_file = image_file.with_suffix('.txt')
        
        if not label_file.exists():
            print(f"⚠️  跳过 {image_file.name}: 未找到对应的标签文件 {label_file.name}")
            continue
        
        # 处理图像
        result = process_single_image(
            segmenter, 
            str(image_file), 
            str(label_file), 
            str(output_path)
        )
        
        if result:
            results.append(result)
    
    return results

def save_batch_results(results, output_dir):
    """保存批量处理结果"""
    if not results:
        return
    
    # 汇总统计
    summary = {
        "total_images": len(results),
        "room_statistics": {},
        "average_room_percentages": {}
    }
    
    # 计算平均统计
    room_totals = {}
    for result in results:
        for room_name, stats in result["room_statistics"].items():
            if room_name not in room_totals:
                room_totals[room_name] = {"pixel_count": 0, "percentage": 0}
            room_totals[room_name]["pixel_count"] += stats["pixel_count"]
            room_totals[room_name]["percentage"] += stats["percentage"]
    
    for room_name, totals in room_totals.items():
        summary["room_statistics"][room_name] = totals
        summary["average_room_percentages"][room_name] = totals["percentage"] / len(results)
    
    # 保存详细结果
    results_file = os.path.join(output_dir, "batch_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "summary": summary,
            "detailed_results": results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 批量处理结果已保存至: {results_file}")
    
    # 打印汇总信息
    print(f"\n📊 批量处理汇总:")
    print(f"   - 总处理图像数: {summary['total_images']}")
    print(f"   - 平均房间占比:")
    for room_name, percentage in summary["average_room_percentages"].items():
        if percentage > 0:
            print(f"     * {room_name}: {percentage:.1f}%")

def main():
    parser = argparse.ArgumentParser(description="房间分割演示脚本")
    parser.add_argument("--input", "-i", required=True, 
                       help="输入图像文件或目录路径")
    parser.add_argument("--output", "-o", required=True,
                       help="输出目录路径")
    parser.add_argument("--label", "-l", 
                       help="标签文件路径 (仅当输入为单个图像时需要)")
    parser.add_argument("--config", "-c",
                       help="配置文件路径 (JSON格式)")
    
    args = parser.parse_args()
    
    # 加载配置
    config = None
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"📋 已加载配置文件: {args.config}")
    
    # 创建分割器
    segmenter = AdvancedRoomSegmenter(config)
    
    print("🏠 房间分割器初始化完成")
    print("📝 支持的房间类型:")
    print("   - bedroom (卧室): bed_grounded, bed_highleg")
    print("   - living_room (客厅): sofa_grounded, sofa_highleg") 
    print("   - kitchen (厨房): dining_table_set")
    print("   - unknown (未知房间): 无特定家具的区域")
    print("   - door (门): 房间分界线")
    
    # 判断输入类型
    if os.path.isfile(args.input):
        # 单个文件处理
        if not args.label:
            # 尝试自动查找标签文件
            label_path = os.path.splitext(args.input)[0] + '.txt'
            if not os.path.exists(label_path):
                print("❌ 错误: 处理单个图像时需要指定标签文件路径")
                return
        else:
            label_path = args.label
        
        print(f"\n🖼️  处理单个图像: {args.input}")
        result = process_single_image(segmenter, args.input, label_path, args.output)
        
        if result:
            # 保存单个结果
            result_file = os.path.join(args.output, "result.json")
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print(f"📄 处理结果已保存至: {result_file}")
    
    elif os.path.isdir(args.input):
        # 批量处理
        print(f"\n📁 批量处理目录: {args.input}")
        results = process_directory(segmenter, args.input, args.output)
        
        if results:
            save_batch_results(results, args.output)
        else:
            print("❌ 没有成功处理任何图像")
    
    else:
        print(f"❌ 错误: 输入路径不存在: {args.input}")

if __name__ == "__main__":
    main()
