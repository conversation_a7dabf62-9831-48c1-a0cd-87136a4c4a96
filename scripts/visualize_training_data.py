#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练数据可视化脚本

用于检查生成的房间分割训练数据质量，包括：
1. 原图与分割掩码的叠加显示
2. 房间区域统计
3. 数据质量检查

作者: AI Assistant
日期: 2025-06-18
"""

import os
import cv2
import numpy as np
import json
import argparse
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def load_class_mapping(mapping_file):
    """加载类别映射"""
    with open(mapping_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_color_overlay(image, mask, class_mapping, alpha=0.5):
    """创建彩色叠加图像"""
    overlay = image.copy()
    
    for class_id, class_info in class_mapping["classes"].items():
        class_id = int(class_id)
        if class_id == 0:  # 跳过unassigned
            continue
            
        color = class_info["color"]
        mask_region = (mask == class_id)
        
        if np.any(mask_region):
            # BGR格式
            overlay[mask_region] = [color[2], color[1], color[0]]
    
    # 混合原图和叠加层
    result = cv2.addWeighted(image, 1-alpha, overlay, alpha, 0)
    return result

def analyze_mask_statistics(mask, class_mapping):
    """分析掩码统计信息"""
    unique_ids, counts = np.unique(mask, return_counts=True)
    total_pixels = mask.shape[0] * mask.shape[1]
    
    stats = {}
    for class_id, count in zip(unique_ids, counts):
        class_id = int(class_id)
        if str(class_id) in class_mapping["classes"]:
            class_name = class_mapping["classes"][str(class_id)]["name"]
            stats[class_name] = {
                "pixel_count": int(count),
                "percentage": float(count / total_pixels * 100),
                "class_id": class_id
            }
    
    return stats

def visualize_single_image(image_path, mask_path, class_mapping, output_path=None):
    """可视化单张图像"""
    # 读取图像和掩码
    image = cv2.imread(image_path)
    mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
    
    if image is None or mask is None:
        raise ValueError(f"无法读取文件: {image_path} 或 {mask_path}")
    
    # 分析统计信息
    stats = analyze_mask_statistics(mask, class_mapping)
    
    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'房间分割训练数据可视化\n{Path(image_path).name}', fontsize=16)
    
    # 原图
    axes[0, 0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('原始图像')
    axes[0, 0].axis('off')
    
    # 分割掩码
    axes[0, 1].imshow(mask, cmap='tab10', vmin=0, vmax=4)
    axes[0, 1].set_title('分割掩码')
    axes[0, 1].axis('off')
    
    # 叠加图像
    overlay = create_color_overlay(image, mask, class_mapping)
    axes[1, 0].imshow(cv2.cvtColor(overlay, cv2.COLOR_BGR2RGB))
    axes[1, 0].set_title('叠加可视化')
    axes[1, 0].axis('off')
    
    # 统计图表
    room_names = []
    percentages = []
    colors = []
    
    for class_name, stat in stats.items():
        if stat["percentage"] > 0:
            room_names.append(f"{class_name}\n({stat['pixel_count']} px)")
            percentages.append(stat["percentage"])
            
            # 获取颜色
            class_id = stat["class_id"]
            if str(class_id) in class_mapping["classes"]:
                color = class_mapping["classes"][str(class_id)]["color"]
                colors.append([c/255.0 for c in color])
            else:
                colors.append([0.5, 0.5, 0.5])
    
    if room_names:
        bars = axes[1, 1].bar(room_names, percentages, color=colors)
        axes[1, 1].set_title('房间面积占比')
        axes[1, 1].set_ylabel('百分比 (%)')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, percentage in zip(bars, percentages):
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.5,
                           f'{percentage:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果已保存: {output_path}")
    else:
        plt.show()
    
    plt.close()
    
    return stats

def batch_visualize(dataset_dir, output_dir=None):
    """批量可视化数据集"""
    dataset_path = Path(dataset_dir)
    
    # 加载类别映射
    mapping_file = dataset_path / "class_mapping.json"
    if not mapping_file.exists():
        raise ValueError(f"未找到类别映射文件: {mapping_file}")
    
    class_mapping = load_class_mapping(mapping_file)
    
    # 查找图像和掩码
    images_dir = dataset_path / "images"
    masks_dir = dataset_path / "masks"
    
    if not images_dir.exists() or not masks_dir.exists():
        raise ValueError("未找到images或masks目录")
    
    image_files = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpg"))
    
    if output_dir:
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
    
    all_stats = []
    
    print(f"开始批量可视化 {len(image_files)} 张图像...")
    
    for image_file in image_files:
        mask_file = masks_dir / f"{image_file.stem}.png"
        
        if not mask_file.exists():
            print(f"⚠️  跳过 {image_file.name}: 未找到对应掩码")
            continue
        
        try:
            if output_dir:
                vis_output = output_path / f"{image_file.stem}_visualization.png"
            else:
                vis_output = None
            
            stats = visualize_single_image(
                str(image_file), str(mask_file), class_mapping, vis_output
            )
            
            stats["filename"] = image_file.name
            all_stats.append(stats)
            
            print(f"✅ 处理完成: {image_file.name}")
            
        except Exception as e:
            print(f"❌ 处理失败 {image_file.name}: {e}")
    
    # 保存统计汇总
    if output_dir and all_stats:
        summary_file = output_path / "visualization_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(all_stats, f, indent=2, ensure_ascii=False)
        print(f"📊 统计汇总已保存: {summary_file}")
    
    return all_stats

def check_data_quality(dataset_dir):
    """检查数据质量"""
    dataset_path = Path(dataset_dir)
    
    # 加载数据集信息
    info_file = dataset_path / "dataset_info.json"
    if info_file.exists():
        with open(info_file, 'r', encoding='utf-8') as f:
            dataset_info = json.load(f)
    else:
        dataset_info = {}
    
    # 检查文件完整性
    images_dir = dataset_path / "images"
    masks_dir = dataset_path / "masks"
    
    image_files = set(f.stem for f in images_dir.glob("*.png")) | set(f.stem for f in images_dir.glob("*.jpg"))
    mask_files = set(f.stem for f in masks_dir.glob("*.png"))
    
    missing_masks = image_files - mask_files
    missing_images = mask_files - image_files
    
    print("📋 数据质量检查报告")
    print("=" * 40)
    print(f"图像文件数: {len(image_files)}")
    print(f"掩码文件数: {len(mask_files)}")
    print(f"匹配对数: {len(image_files & mask_files)}")
    
    if missing_masks:
        print(f"⚠️  缺少掩码的图像: {missing_masks}")
    
    if missing_images:
        print(f"⚠️  缺少图像的掩码: {missing_images}")
    
    if dataset_info:
        print(f"\n📊 数据集统计:")
        print(f"总处理图像: {dataset_info.get('total_images', 'N/A')}")
        print(f"成功处理: {dataset_info.get('successful_images', 'N/A')}")
        
        if 'room_statistics' in dataset_info:
            print(f"房间像素统计:")
            for room, count in dataset_info['room_statistics'].items():
                print(f"  - {room}: {count:,} 像素")

def main():
    parser = argparse.ArgumentParser(description="训练数据可视化工具")
    parser.add_argument("--dataset", "-d", required=True,
                       help="数据集目录路径")
    parser.add_argument("--output", "-o",
                       help="输出目录路径 (可选)")
    parser.add_argument("--single", "-s",
                       help="可视化单张图像 (指定图像文件名)")
    parser.add_argument("--check", action="store_true",
                       help="检查数据质量")
    
    args = parser.parse_args()
    
    print("🔍 房间分割训练数据可视化工具")
    print("=" * 50)
    
    try:
        if args.check:
            check_data_quality(args.dataset)
        
        if args.single:
            # 单张图像可视化
            dataset_path = Path(args.dataset)
            mapping_file = dataset_path / "class_mapping.json"
            class_mapping = load_class_mapping(mapping_file)
            
            image_file = dataset_path / "images" / args.single
            mask_file = dataset_path / "masks" / f"{Path(args.single).stem}.png"
            
            if args.output:
                output_file = Path(args.output) / f"{Path(args.single).stem}_visualization.png"
            else:
                output_file = None
            
            stats = visualize_single_image(str(image_file), str(mask_file), 
                                         class_mapping, output_file)
            
            print("📊 图像统计:")
            for room, stat in stats.items():
                print(f"  - {room}: {stat['pixel_count']} 像素 ({stat['percentage']:.1f}%)")
        
        else:
            # 批量可视化
            batch_visualize(args.dataset, args.output)
    
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
