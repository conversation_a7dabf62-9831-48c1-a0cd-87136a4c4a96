#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整工作流程演示

展示从目标检测结果到房间分割训练数据的完整流程：
1. 基于家具检测推断房间区域
2. 房间扩展到门边界
3. 识别unknown房间
4. 生成训练数据
5. 可视化验证

作者: AI Assistant
日期: 2025-06-18
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_room_segmenter import AdvancedRoomSegmenter

def demo_complete_workflow():
    """演示完整的工作流程"""
    
    print("🏠 家居地图房间分割完整工作流程演示")
    print("=" * 60)
    
    # 配置路径
    input_dir = "datasets/test"
    output_dir = "scripts/demo_complete"
    
    print(f"📁 输入目录: {input_dir}")
    print(f"📁 输出目录: {output_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 步骤1: 创建优化的分割器配置
    print(f"\n🔧 步骤1: 配置房间分割器")
    config = {
        "edge_threshold1": 80,          # 边缘检测低阈值
        "edge_threshold2": 100,         # 边缘检测高阈值
        "door_barrier_width": 5,        # 门区域宽度 (小值让房间更好扩展)
        "expansion_iterations": 25,     # 扩展迭代次数 (高值确保到达门)
        "min_room_area": 3000,           # 最小房间面积
        "morphology_kernel_size": 3,    # 形态学核大小
        "gaussian_blur_size": 3         # 高斯模糊大小
    }
    
    print("   配置参数:")
    for key, value in config.items():
        print(f"   - {key}: {value}")
    
    # 保存配置
    config_file = os.path.join(output_dir, "segmentation_config.json")
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"   ✅ 配置已保存: {config_file}")
    
    # 步骤2: 创建分割器
    print(f"\n🤖 步骤2: 初始化房间分割器")
    segmenter = AdvancedRoomSegmenter(config)
    print("   ✅ 分割器初始化完成")
    
    # 步骤3: 处理示例图像
    print(f"\n🖼️  步骤3: 处理示例图像")
    image_path = "datasets/test/9176a0d5-shunzao_10499.png"
    label_path = "datasets/test/9176a0d5-shunzao_10499.txt"
    
    if not os.path.exists(image_path) or not os.path.exists(label_path):
        print(f"   ❌ 找不到测试文件: {image_path} 或 {label_path}")
        return
    
    # 处理单张图像
    result = segmenter.process_image(image_path, label_path, output_dir)
    
    print(f"   ✅ 图像处理完成")
    print(f"   📊 房间统计:")
    for room_name, stats in result["room_statistics"].items():
        if stats["pixel_count"] > 0:
            print(f"      - {room_name}: {stats['pixel_count']} 像素 ({stats['percentage']:.1f}%)")
    
    # 步骤4: 生成训练数据集
    print(f"\n📚 步骤4: 生成训练数据集")
    training_dir = os.path.join(output_dir, "training_dataset")
    
    dataset_stats = segmenter.generate_training_dataset(
        input_dir, training_dir, format_type="mask"
    )
    
    print(f"   ✅ 训练数据生成完成")
    print(f"   📁 训练数据目录: {training_dir}")
    
    # 步骤5: 创建数据集划分
    print(f"\n📋 步骤5: 创建数据集划分")
    
    # 由于只有一张图像，创建简单的划分信息
    split_info = {
        "train": [],
        "val": [],
        "test": [f["image"] for f in dataset_stats["files"]],
        "note": "单图像演示，所有数据分配给测试集"
    }
    
    split_file = os.path.join(training_dir, "dataset_split.json")
    with open(split_file, 'w', encoding='utf-8') as f:
        json.dump(split_info, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 数据集划分完成: {split_file}")
    
    # 步骤6: 验证结果
    print(f"\n✅ 步骤6: 验证生成的数据")
    
    # 检查文件完整性
    images_dir = Path(training_dir) / "images"
    masks_dir = Path(training_dir) / "masks"
    
    image_files = list(images_dir.glob("*.png"))
    mask_files = list(masks_dir.glob("*.png"))
    
    print(f"   📁 生成的文件:")
    print(f"      - 图像文件: {len(image_files)} 个")
    print(f"      - 掩码文件: {len(mask_files)} 个")
    print(f"      - 类别映射: class_mapping.json")
    print(f"      - 数据集信息: dataset_info.json")
    print(f"      - 数据集划分: dataset_split.json")
    
    # 步骤7: 展示关键改进
    print(f"\n⭐ 步骤7: 关键改进说明")
    print(f"   🔑 核心改进:")
    print(f"      1. 房间区域扩展到门边界")
    print(f"         - 有床的房间会包含到达门的完整区域")
    print(f"         - 门不再是分界线，而是房间的一部分")
    print(f"      ")
    print(f"      2. Unknown房间自动识别")
    print(f"         - 不包含特定家具的区域标记为unknown")
    print(f"         - 形成独立的房间区域用于训练")
    print(f"      ")
    print(f"      3. 适合训练的数据格式")
    print(f"         - 每个像素值代表房间类别ID")
    print(f"         - 完整的房间分割而非家具边界框")
    
    # 步骤8: 使用建议
    print(f"\n💡 步骤8: 使用建议")
    print(f"   📖 如何使用生成的训练数据:")
    print(f"      1. 检查可视化结果确保质量")
    print(f"      2. 根据需要调整配置参数")
    print(f"      3. 使用掩码图像训练分割模型")
    print(f"      4. 参考class_mapping.json了解类别编码")
    
    print(f"\n   🔧 参数调优建议:")
    print(f"      - expansion_iterations: 控制房间扩展范围")
    print(f"      - min_room_area: 过滤小区域噪声")
    print(f"      - door_barrier_width: 影响门区域大小")
    print(f"      - edge_threshold1/2: 控制边缘检测敏感度")
    
    # 最终总结
    print(f"\n🎉 工作流程演示完成!")
    print(f"📁 所有结果已保存至: {output_dir}")
    print(f"📚 训练数据已保存至: {training_dir}")
    
    # 返回结果路径供进一步使用
    return {
        "output_dir": output_dir,
        "training_dir": training_dir,
        "config_file": config_file,
        "result": result,
        "dataset_stats": dataset_stats
    }

def print_file_structure(base_dir):
    """打印生成的文件结构"""
    print(f"\n📂 生成的文件结构:")
    
    for root, dirs, files in os.walk(base_dir):
        level = root.replace(base_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")

if __name__ == "__main__":
    try:
        # 运行完整工作流程
        results = demo_complete_workflow()
        
        # 打印文件结构
        print_file_structure(results["output_dir"])
        
        print(f"\n✨ 演示成功完成!")
        print(f"🔍 您可以查看生成的文件来验证结果质量")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
