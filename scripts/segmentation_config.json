{"edge_threshold1": 50, "edge_threshold2": 150, "door_barrier_width": 20, "expansion_iterations": 30, "min_room_area": 800, "morphology_kernel_size": 5, "gaussian_blur_size": 5, "description": "房间分割配置文件", "parameters_description": {"edge_threshold1": "Canny边缘检测低阈值 (建议范围: 30-80)", "edge_threshold2": "Canny边缘检测高阈值 (建议范围: 100-200)", "door_barrier_width": "门分界线宽度，像素单位 (建议范围: 10-30)", "expansion_iterations": "区域扩展迭代次数 (建议范围: 15-40)", "min_room_area": "最小房间面积，像素单位 (建议范围: 500-2000)", "morphology_kernel_size": "形态学操作核大小 (建议范围: 3-7)", "gaussian_blur_size": "高斯模糊核大小 (建议范围: 3-9，必须为奇数)"}, "room_types": {"bedroom": {"furniture": ["bed_grounded", "bed_highleg"], "color": [0, 0, 255], "description": "卧室 - 包含床类家具的区域"}, "living_room": {"furniture": ["sofa_grounded", "sofa_highleg"], "color": [0, 255, 0], "description": "客厅 - 包含沙发类家具的区域"}, "kitchen": {"furniture": ["dining_table_set"], "color": [255, 0, 0], "description": "厨房 - 包含餐桌椅的区域"}, "unknown": {"furniture": [], "color": [128, 128, 128], "description": "未知房间 - 不包含特定家具的独立区域"}, "door": {"furniture": ["door"], "color": [255, 255, 0], "description": "门 - 房间之间的分界线"}}}