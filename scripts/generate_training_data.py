#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成房间分割训练数据脚本

基于目标检测结果生成房间分割的训练数据，支持多种格式输出。
房间区域会扩展到最近的门，形成完整的房间分割。

作者: AI Assistant
日期: 2025-06-18
"""

import os
import sys
import argparse
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_room_segmenter import AdvancedRoomSegmenter

def create_training_config():
    """创建适合训练数据生成的配置"""
    return {
        "edge_threshold1": 30,          # 降低边缘检测阈值，保留更多细节
        "edge_threshold2": 100,         # 降低边缘检测阈值
        "door_barrier_width": 5,        # 减小门区域，让房间更好地扩展
        "expansion_iterations": 50,     # 增加扩展迭代次数，确保到达门边界
        "min_room_area": 500,           # 降低最小面积，保留更多小房间
        "morphology_kernel_size": 3,    # 减小核大小，保持细节
        "gaussian_blur_size": 3         # 减小模糊，保持边缘清晰
    }

def validate_input_directory(input_dir):
    """验证输入目录"""
    input_path = Path(input_dir)
    if not input_path.exists():
        raise ValueError(f"输入目录不存在: {input_dir}")
    
    # 检查是否有图像和标签文件
    image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    image_files = []
    for ext in image_extensions:
        image_files.extend(input_path.glob(f"*{ext}"))
        image_files.extend(input_path.glob(f"*{ext.upper()}"))
    
    if not image_files:
        raise ValueError(f"输入目录中未找到图像文件: {input_dir}")
    
    # 检查标签文件
    label_files = list(input_path.glob("*.txt"))
    if not label_files:
        raise ValueError(f"输入目录中未找到标签文件: {input_dir}")
    
    print(f"✅ 输入验证通过:")
    print(f"   - 图像文件: {len(image_files)} 个")
    print(f"   - 标签文件: {len(label_files)} 个")
    
    return len(image_files), len(label_files)

def create_dataset_split(dataset_stats, output_dir, train_ratio=0.8, val_ratio=0.1):
    """创建数据集划分"""
    import random
    
    files = dataset_stats["files"]
    random.shuffle(files)
    
    total_files = len(files)
    train_count = int(total_files * train_ratio)
    val_count = int(total_files * val_ratio)
    
    train_files = files[:train_count]
    val_files = files[train_count:train_count + val_count]
    test_files = files[train_count + val_count:]
    
    # 保存划分信息
    split_info = {
        "train": [f["image"] for f in train_files],
        "val": [f["image"] for f in val_files],
        "test": [f["image"] for f in test_files],
        "statistics": {
            "total": total_files,
            "train": len(train_files),
            "val": len(val_files),
            "test": len(test_files)
        }
    }
    
    split_file = Path(output_dir) / "dataset_split.json"
    with open(split_file, 'w', encoding='utf-8') as f:
        json.dump(split_info, f, indent=2, ensure_ascii=False)
    
    print(f"📋 数据集划分:")
    print(f"   - 训练集: {len(train_files)} ({len(train_files)/total_files*100:.1f}%)")
    print(f"   - 验证集: {len(val_files)} ({len(val_files)/total_files*100:.1f}%)")
    print(f"   - 测试集: {len(test_files)} ({len(test_files)/total_files*100:.1f}%)")
    print(f"   - 划分信息保存至: {split_file}")
    
    return split_info

def create_class_mapping(output_dir):
    """创建类别映射文件"""
    class_mapping = {
        "classes": {
            0: {"name": "unassigned", "color": [128, 128, 128], "description": "未分配区域"},
            1: {"name": "bedroom", "color": [255, 0, 0], "description": "卧室"},
            2: {"name": "living_room", "color": [0, 255, 0], "description": "客厅"},
            3: {"name": "kitchen", "color": [0, 0, 255], "description": "厨房"},
            4: {"name": "unknown", "color": [128, 128, 128], "description": "未知房间"}
        },
        "furniture_mapping": {
            "bed_grounded": "bedroom",
            "bed_highleg": "bedroom",
            "sofa_grounded": "living_room",
            "sofa_highleg": "living_room",
            "dining_table_set": "kitchen",
            "door": "boundary"
        },
        "training_info": {
            "input_format": "YOLO detection boxes",
            "output_format": "Segmentation masks",
            "mask_encoding": "Each pixel value represents class ID",
            "room_expansion": "Rooms expand to nearest doors"
        }
    }
    
    mapping_file = Path(output_dir) / "class_mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(class_mapping, f, indent=2, ensure_ascii=False)
    
    print(f"📝 类别映射文件已保存: {mapping_file}")
    return mapping_file

def main():
    parser = argparse.ArgumentParser(description="生成房间分割训练数据")
    parser.add_argument("--input", "-i", required=True,
                       help="输入目录路径 (包含图像和YOLO标签文件)")
    parser.add_argument("--output", "-o", required=True,
                       help="输出目录路径")
    parser.add_argument("--format", "-f", choices=["mask", "coco"], default="mask",
                       help="输出格式 (mask: 灰度掩码, coco: COCO分割格式)")
    parser.add_argument("--config", "-c",
                       help="自定义配置文件路径")
    parser.add_argument("--split", action="store_true",
                       help="是否创建训练/验证/测试集划分")
    parser.add_argument("--train-ratio", type=float, default=0.8,
                       help="训练集比例 (默认: 0.8)")
    parser.add_argument("--val-ratio", type=float, default=0.1,
                       help="验证集比例 (默认: 0.1)")
    
    args = parser.parse_args()
    
    print("🏠 房间分割训练数据生成器")
    print("=" * 50)
    
    try:
        # 验证输入
        img_count, label_count = validate_input_directory(args.input)
        
        # 加载配置
        if args.config and os.path.exists(args.config):
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"📋 使用自定义配置: {args.config}")
        else:
            config = create_training_config()
            print("📋 使用默认训练配置")
        
        # 创建分割器
        segmenter = AdvancedRoomSegmenter(config)
        
        print(f"\n🔧 配置参数:")
        for key, value in config.items():
            print(f"   - {key}: {value}")
        
        print(f"\n🎯 房间扩展策略:")
        print(f"   - 房间区域会扩展到最近的门边界")
        print(f"   - 门被包含在房间内，而不是作为独立区域")
        print(f"   - 未包含特定家具的区域标记为unknown房间")
        
        # 生成训练数据
        print(f"\n🚀 开始生成训练数据...")
        dataset_stats = segmenter.generate_training_dataset(
            args.input, args.output, args.format
        )
        
        # 创建类别映射
        create_class_mapping(args.output)
        
        # 创建数据集划分
        if args.split:
            create_dataset_split(dataset_stats, args.output, 
                               args.train_ratio, args.val_ratio)
        
        # 打印最终统计
        print(f"\n📊 最终统计:")
        print(f"   - 输入图像: {dataset_stats['total_images']}")
        print(f"   - 成功处理: {dataset_stats['successful_images']}")
        print(f"   - 成功率: {dataset_stats['successful_images']/dataset_stats['total_images']*100:.1f}%")
        
        if dataset_stats['room_statistics']:
            print(f"   - 房间像素统计:")
            for room_name, pixel_count in dataset_stats['room_statistics'].items():
                if pixel_count > 0:
                    print(f"     * {room_name}: {pixel_count:,} 像素")
        
        print(f"\n✅ 训练数据生成完成!")
        print(f"📁 输出目录: {args.output}")
        print(f"📄 数据格式: {args.format}")
        
        # 使用建议
        print(f"\n💡 使用建议:")
        print(f"   1. 检查生成的掩码图像确保质量")
        print(f"   2. 根据需要调整配置参数重新生成")
        print(f"   3. 使用dataset_split.json进行模型训练")
        print(f"   4. 参考class_mapping.json了解类别信息")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
