# 家居地图房间分割系统

基于目标检测结果推断完整房间区域的智能分割系统。

## 🎯 功能特点

### 核心功能
- **智能房间推断**: 基于家具检测结果推断完整房间区域，而不仅仅是家具边界框
- **门分界线**: 使用门作为房间之间的自然分界线
- **Unknown房间识别**: 自动识别不包含特定家具的独立区域为unknown房间
- **多房间类型支持**: 支持卧室、客厅、厨房、未知房间和门的分割

### 支持的家具类别
```
0: bed_grounded    → bedroom (卧室)
1: bed_highleg     → bedroom (卧室)  
2: sofa_grounded   → living_room (客厅)
3: sofa_highleg    → living_room (客厅)
4: door            → 房间分界线
5: dining_table_set → kitchen (厨房)
```

### 房间类型
- **bedroom (卧室)**: 包含床类家具的区域 🛏️
- **living_room (客厅)**: 包含沙发类家具的区域 🛋️
- **kitchen (厨房)**: 包含餐桌椅的区域 🍽️
- **unknown (未知房间)**: 不包含特定家具的独立区域 ❓
- **door (门)**: 房间之间的分界线 🚪

## 📁 文件结构

```
datasets/
├── mask.py                      # 基础分割器
├── advanced_room_segmenter.py   # 高级分割器 (推荐)
├── room_segmentation_demo.py    # 演示脚本
├── segmentation_config.json     # 配置文件示例
├── test/                        # 测试数据
│   ├── 9176a0d5-shunzao_10499.png
│   └── 9176a0d5-shunzao_10499.txt
└── room_masks/                  # 输出结果
```

## 🚀 快速开始

### 1. 环境要求
```bash
pip install opencv-python numpy scipy
```

### 2. 处理单张图像
```bash
python room_segmentation_demo.py \
    --input datasets/test/9176a0d5-shunzao_10499.png \
    --label datasets/test/9176a0d5-shunzao_10499.txt \
    --output results/
```

### 3. 批量处理目录
```bash
python room_segmentation_demo.py \
    --input datasets/test/ \
    --output results/
```

### 4. 使用自定义配置
```bash
python room_segmentation_demo.py \
    --input datasets/test/ \
    --output results/ \
    --config segmentation_config.json
```

## 🔧 配置参数

### 主要参数说明
```json
{
  "edge_threshold1": 50,        // Canny边缘检测低阈值
  "edge_threshold2": 150,       // Canny边缘检测高阈值  
  "door_barrier_width": 20,     // 门分界线宽度(像素)
  "expansion_iterations": 30,   // 区域扩展迭代次数
  "min_room_area": 800,         // 最小房间面积(像素)
  "morphology_kernel_size": 5,  // 形态学操作核大小
  "gaussian_blur_size": 5       // 高斯模糊核大小
}
```

### 参数调优建议
- **edge_threshold1/2**: 控制边缘检测敏感度，值越小检测到的边缘越多
- **door_barrier_width**: 门分界线宽度，影响房间分离效果
- **expansion_iterations**: 区域扩展次数，影响房间覆盖范围
- **min_room_area**: 过滤小区域，避免噪声

## 📊 输出结果

### 文件输出
- `*_segmented.png`: 原图与分割结果叠加的可视化图像
- `*_mask.png`: 纯分割掩码 (灰度图)
- `*_color_mask.png`: 彩色分割掩码
- `result.json` / `batch_results.json`: 详细统计信息

### 统计信息示例
```json
{
  "room_statistics": {
    "bedroom": {
      "pixel_count": 10381,
      "percentage": 17.6
    },
    "living_room": {
      "pixel_count": 10009, 
      "percentage": 17.0
    },
    "kitchen": {
      "pixel_count": 4047,
      "percentage": 6.9
    },
    "unknown": {
      "pixel_count": 31369,
      "percentage": 53.1
    },
    "door": {
      "pixel_count": 457,
      "percentage": 0.8
    }
  }
}
```

## 🎨 颜色编码

- 🔴 **卧室 (bedroom)**: 红色 (0, 0, 255)
- 🟢 **客厅 (living_room)**: 绿色 (0, 255, 0)  
- 🔵 **厨房 (kitchen)**: 蓝色 (255, 0, 0)
- ⚫ **未知房间 (unknown)**: 灰色 (128, 128, 128)
- 🟡 **门 (door)**: 青色 (255, 255, 0)

## 🔬 算法原理

### 1. 边界检测
- 使用Canny算子检测图像边缘
- 形态学操作连接断裂边缘
- 高斯模糊减少噪声

### 2. 门边界处理 ⭐ **改进重点**
- **门不再作为分界线，而是房间的一部分**
- 房间区域会扩展到包含门在内的完整区域
- 门成为连接不同房间的通道，而不是障碍

### 3. 区域扩展到门
- 以家具检测框作为种子点
- **迭代扩展直到遇到图像边界，允许通过门区域**
- 每个房间都会扩展到最近的门，形成完整房间区域

### 4. Unknown房间识别
- 连通组件分析找到未分配区域
- 过滤小面积噪声区域
- **不包含特定家具的独立区域标记为unknown房间**
- Unknown房间也可以包含门区域

## 📝 YOLO标签格式

输入标签文件应为YOLO格式 (.txt):
```
class_id x_center y_center width height
```

示例:
```
4 0.846066 0.503421 0.059293 0.017104  # door
1 0.132269 0.207526 0.168757 0.180160  # bed_highleg  
3 0.519954 0.282782 0.228050 0.403649  # sofa_highleg
5 0.420182 0.731471 0.140251 0.163056  # dining_table_set
```

## 🛠️ 高级用法

### 编程接口
```python
from advanced_room_segmenter import AdvancedRoomSegmenter

# 创建分割器
segmenter = AdvancedRoomSegmenter(config={
    "expansion_iterations": 25,
    "min_room_area": 1000
})

# 处理图像
result = segmenter.process_image(
    image_path="image.png",
    label_path="labels.txt", 
    output_dir="output/"
)

# 获取分割结果
color_map, segmentation = segmenter.create_room_segmentation(image, detections)
```

### 自定义房间类型
可以通过修改 `class_info` 字典来支持新的家具类型和房间映射。

## 🐛 常见问题

### Q: 分割结果不准确怎么办？
A: 尝试调整以下参数:
- 增加 `expansion_iterations` 扩大房间覆盖范围
- 调整 `edge_threshold1/2` 改变边缘检测敏感度
- 修改 `door_barrier_width` 调整门分界线宽度

### Q: Unknown房间太多或太少？
A: 调整 `min_room_area` 参数过滤小区域，或检查门的检测是否准确。

### Q: 房间边界不清晰？
A: 增加 `morphology_kernel_size` 或调整 `gaussian_blur_size` 改善边缘连续性。

## 🎯 生成训练数据

### 快速生成训练数据
```bash
# 生成用于分割模型训练的数据
python generate_training_data.py \
    --input datasets/test/ \
    --output datasets/training_data/ \
    --split

# 可视化检查数据质量
python visualize_training_data.py \
    --dataset datasets/training_data/ \
    --output datasets/visualizations/ \
    --check
```

### 训练数据格式
生成的训练数据包含：
- `images/`: 原始图像
- `masks/`: 分割掩码 (每个像素值代表房间类别ID)
- `class_mapping.json`: 类别映射信息
- `dataset_split.json`: 训练/验证/测试集划分
- `dataset_info.json`: 数据集统计信息

### 掩码编码
```
像素值 0: unassigned (未分配)
像素值 1: bedroom (卧室)
像素值 2: living_room (客厅)
像素值 3: kitchen (厨房)
像素值 4: unknown (未知房间)
```

### 关键改进 ⭐
1. **房间扩展到门**: 有床的房间会扩展到包含门在内的完整区域
2. **门作为房间一部分**: 门不再是分界线，而是房间的组成部分
3. **完整房间分割**: 生成的掩码代表完整的房间区域，适合训练分割模型

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
