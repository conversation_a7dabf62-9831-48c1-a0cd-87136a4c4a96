# import os
# import cv2
# import numpy as np
# from scipy.ndimage import binary_fill_holes

# class ContourBasedSegmenter:
#     def __init__(self):
#         # 类别定义
#         self.class_info = {
#             0: {"name": "bed_grounded", "room": "bedroom"},
#             1: {"name": "bed_highleg", "room": "bedroom"},
#             2: {"name": "sofa_grounded", "room": "living_room"},
#             3: {"name": "sofa_highleg", "room": "living_room"},
#             4: {"name": "door", "room": None},
#             5: {"name": "dining_table_set", "room": "kitchen"}
#         }
        
#         # 颜色映射
#         self.room_colors = {
#             "bedroom": (0, 0, 255),      # 红色
#             "living_room": (0, 255, 0),   # 绿色
#             "kitchen": (255, 0, 0),       # 蓝色
#             "door": (255, 255, 0),        # 青色
#             "unknown": (128, 128, 128)    # 灰色
#         }
        
#         # 参数配置
#         self.door_barrier_width = 5       # 门禁区域宽度
#         self.contour_sample_step = 2       # 轮廓采样步长
#         self.min_room_area = 30          # 最小房间面积
#         self.edge_smooth_size = 3          # 边缘平滑核大小

#     def load_yolo_labels(self, label_path, img_size):
#         """解析YOLO格式标签文件"""
#         detections = []
#         img_width, img_height = img_size
        
#         if not os.path.exists(label_path):
#             return detections
            
#         with open(label_path, 'r') as f:
#             for line in f:
#                 parts = line.strip().split()
#                 if len(parts) < 5:
#                     continue
                    
#                 # 解析YOLO格式
#                 class_id = int(parts[0])
#                 x_center = float(parts[1]) * img_width
#                 y_center = float(parts[2]) * img_height
#                 width = float(parts[3]) * img_width
#                 height = float(parts[4]) * img_height
                
#                 # 计算边界框
#                 x1 = int(x_center - width/2)
#                 y1 = int(y_center - height/2)
#                 x2 = int(x_center + width/2)
#                 y2 = int(y_center + height/2)
                
#                 # 确保坐标在图像范围内
#                 x1 = max(0, min(x1, img_width-1))
#                 y1 = max(0, min(y1, img_height-1))
#                 x2 = max(0, min(x2, img_width-1))
#                 y2 = max(0, min(y2, img_height-1))
                
#                 detections.append((x1, y1, x2, y2, class_id))
                
#         return detections

#     def create_contour_based_segmentation(self, detections, img_size):
#         """基于轮廓创建分割"""
#         height, width = img_size
        
#         # 初始化掩码
#         door_mask = np.zeros((height, width), dtype=np.uint8)
#         object_masks = {
#             "bedroom": np.zeros((height, width), dtype=np.uint8),
#             "living_room": np.zeros((height, width), dtype=np.uint8),
#             "kitchen": np.zeros((height, width), dtype=np.uint8)
#         }
        
#         # 1. 标记检测对象和门位置
#         for (x1, y1, x2, y2, class_id) in detections:
#             class_info = self.class_info.get(class_id)
#             if not class_info:
#                 continue
                
#             if class_info["name"] == "door":
#                 cv2.rectangle(door_mask, (x1, y1), (x2, y2), 1, -1)
#             elif class_info["room"] in object_masks:
#                 room_type = class_info["room"]
#                 cv2.rectangle(object_masks[room_type], (x1, y1), (x2, y2), 1, -1)
        
#         # 2. 创建门禁区域
#         kernel = np.ones((self.door_barrier_width, self.door_barrier_width), np.uint8)
#         door_barrier = cv2.dilate(door_mask, kernel)
        
#         # 3. 初始化分割结果
#         segmentation = np.zeros((height, width), dtype=np.uint8)
        
#         # 4. 处理每个房间类型
#         for idx, room_type in enumerate(["bedroom", "living_room", "kitchen"]):
#             # 填充对象区域
#             filled = binary_fill_holes(object_masks[room_type]).astype(np.uint8)
            
#             # 获取所有轮廓
#             contours, _ = cv2.findContours(filled, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
#             for cnt in contours:
#                 # 计算轮廓面积
#                 area = cv2.contourArea(cnt)
#                 if area < self.min_room_area:
#                     continue
                
#                 # 创建临时掩码用于当前区域
#                 region_mask = np.zeros((height, width), dtype=np.uint8)
#                 cv2.drawContours(region_mask, [cnt], -1, 1, -1)
                
#                 # 从轮廓点进行区域生长
#                 for i in range(0, len(cnt), self.contour_sample_step):
#                     x, y = cnt[i][0]
                    
#                     # 创建生长掩码 (必须比原图大2像素)
#                     grow_mask = np.zeros((height+2, width+2), dtype=np.uint8)
#                     grow_mask[1:-1, 1:-1] = door_barrier
                    
#                     # 执行区域生长
#                     cv2.floodFill(
#                         region_mask, 
#                         grow_mask, 
#                         (x, y), 
#                         1,  # 填充值
#                         flags=4 | (255 << 8)  # 4: 仅考虑4连通区域
#                     )
                
#                 # 应用门禁限制
#                 region_mask[door_barrier == 1] = 0
                
#                 # 合并到最终分割
#                 segmentation[region_mask == 1] = idx + 1
        
#         # 5. 边缘平滑处理
#         kernel = np.ones((self.edge_smooth_size, self.edge_smooth_size), np.uint8)
#         segmentation = cv2.morphologyEx(segmentation, cv2.MORPH_CLOSE, kernel)
        
#         # 6. 创建彩色可视化
#         color_map = np.zeros((height, width, 3), dtype=np.uint8)
#         for idx, room in enumerate(["bedroom", "living_room", "kitchen"]):
#             color_map[segmentation == idx+1] = self.room_colors[room]
#         color_map[door_mask == 1] = self.room_colors["door"]
#         color_map[segmentation == 0] = self.room_colors["unknown"]
        
#         return color_map

#     def process_image(self, image_path, label_path, output_path):
#         """处理单张图像"""
#         img = cv2.imread(image_path)
#         if img is None:
#             print(f"无法读取图像: {image_path}")
#             return
            
#         height, width = img.shape[:2]
#         detections = self.load_yolo_labels(label_path, (width, height))
        
#         # 创建分割掩码
#         room_mask = self.create_contour_based_segmentation(detections, (height, width))
        
#         # 可视化叠加
#         result = cv2.addWeighted(img, 0.7, room_mask, 0.3, 0)
#         cv2.imwrite(output_path, result)
#         print(f"处理完成: {image_path}")


# # 使用示例
# if __name__ == "__main__":
#     # 配置路径
#     IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
#     LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
#     OUTPUT_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/room_masks"
    
#     os.makedirs(OUTPUT_DIR, exist_ok=True)
    
#     # 创建分割器
#     segmenter = ContourBasedSegmenter()
    
#     # 处理所有图像
#     for img_name in os.listdir(IMAGE_DIR):
#         if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
#             img_path = os.path.join(IMAGE_DIR, img_name)
#             label_name = os.path.splitext(img_name)[0] + '.txt'
#             label_path = os.path.join(LABEL_DIR, label_name)
#             output_path = os.path.join(OUTPUT_DIR, f"seg_{img_name}")
            
#             segmenter.process_image(img_path, label_path, output_path)



import os
import cv2
import numpy as np
from scipy.ndimage import binary_fill_holes, distance_transform_edt

class ImprovedRoomSegmenter:
    def __init__(self):
        # 类别定义
        self.class_info = {
            0: {"name": "bed_grounded", "room": "bedroom"},
            1: {"name": "bed_highleg", "room": "bedroom"},
            2: {"name": "sofa_grounded", "room": "living_room"},
            3: {"name": "sofa_highleg", "room": "living_room"},
            4: {"name": "door", "room": None},
            5: {"name": "dining_table_set", "room": "kitchen"}
        }

        # 颜色映射
        self.room_colors = {
            "bedroom": (0, 0, 255),      # 红色
            "living_room": (0, 255, 0),  # 绿色
            "kitchen": (255, 0, 0),      # 蓝色
            "door": (255, 255, 0),       # 青色
            "unknown": (255, 0, 255)  # 灰色
        }

        # 参数配置
        self.edge_threshold1 = 50      # Canny边缘检测低阈值
        self.edge_threshold2 = 200      # Canny边缘检测高阈值
        self.door_barrier_width = 3    # 门分界线宽度
        self.expansion_iterations = 30  # 区域扩展迭代次数
        self.min_room_area = 3000        # 最小房间面积
        # self.unknown_expansion_iterations = 15  # unknown区域扩展迭代次数

    def detect_room_edges(self, image):
        """检测房间边缘"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 使用Canny检测边缘
        edges = cv2.Canny(gray, self.edge_threshold1, self.edge_threshold2)

        # 膨胀边缘使其更连续
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.dilate(edges, kernel, iterations=1)

        return edges

    def expand_region_to_doors(self, seed_mask, edge_boundaries, door_mask, iterations=50):
        """扩展区域直到遇到门或图像边界 - 房间应该扩展到门的位置"""
        current_mask = seed_mask.copy()
        kernel = np.ones((3, 3), np.uint8)

        print(f"开始区域扩展，初始种子区域像素数: {np.sum(seed_mask)}")
        print(f"门区域像素数: {np.sum(door_mask)}")

        for i in range(iterations):
            # 膨胀当前区域
            expanded = cv2.dilate(current_mask, kernel, iterations=1)

            # 只被图像边缘阻止，不被门阻止
            # 房间应该扩展到包含门的位置
            expanded[edge_boundaries > 0] = 0

            # 如果没有新的扩展，停止
            if np.array_equal(expanded, current_mask):
                print(f"区域扩展在第{i+1}次迭代后收敛，最终像素数: {np.sum(expanded)}")
                break

            current_mask = expanded

        return current_mask

    def expand_room_to_door_boundary(self, seed_mask, door_mask, edge_boundaries):
        """扩展房间区域直到遇到门边界 - 门作为房间分界线"""
        height, width = seed_mask.shape

        # 1. 创建门边界 - 门及其周围区域作为停止边界
        door_boundary = cv2.dilate(door_mask, np.ones((3, 3), np.uint8), iterations=2)

        # 2. 创建组合边界：图像边缘 + 门边界
        combined_boundaries = np.maximum(edge_boundaries > 100, door_boundary)

        # 3. 从种子区域开始扩展，直到遇到门边界
        result_mask = seed_mask.copy()
        kernel = np.ones((3, 3), np.uint8)

        print(f"开始扩展，种子区域: {np.sum(seed_mask)} 像素")
        print(f"门边界区域: {np.sum(door_boundary)} 像素")

        # 迭代扩展，直到遇到门或图像边界
        for i in range(50):
            # 膨胀当前区域
            expanded = cv2.dilate(result_mask, kernel, iterations=1)

            # 移除边界区域（包括门边界）
            expanded[combined_boundaries > 0] = 0

            # 如果没有新的扩展，停止
            if np.array_equal(expanded, result_mask):
                print(f"扩展在第{i+1}次迭代后停止，最终区域: {np.sum(expanded)} 像素")
                break

            result_mask = expanded

        # 4. 房间扩展到门边界，但不包含门本身
        # 门保持独立，作为房间之间的分界

        return result_mask

    def expand_room_with_door_boundaries(self, seed_mask, boundaries):
        """扩展房间区域，以门和图像边缘为边界"""
        result_mask = seed_mask.copy()
        kernel = np.ones((3, 3), np.uint8)

        print(f"开始扩展房间，种子区域: {np.sum(seed_mask)} 像素")

        # 迭代扩展，直到遇到边界
        for i in range(30):
            # 膨胀当前区域
            expanded = cv2.dilate(result_mask, kernel, iterations=1)

            # 移除边界区域
            expanded[boundaries > 0] = 0

            # 如果没有新的扩展，停止
            if np.array_equal(expanded, result_mask):
                print(f"房间扩展在第{i+1}次迭代后停止，最终区域: {np.sum(expanded)} 像素")
                break

            result_mask = expanded

        return result_mask

    def expand_region_iteratively(self, seed_mask, boundary_mask, iterations=20):
        """保持向后兼容的方法"""
        return self.expand_region_to_doors(seed_mask, boundary_mask,
                                         np.zeros_like(seed_mask), iterations)

    def create_door_boundary_segmentation(self, image, detections):
        """创建以门为分界线的房间分割"""
        height, width = image.shape[:2]
        print(f"开始处理图像: {width}x{height}")

        # 1. 检测图像中的边缘
        edge_map = self.detect_room_edges(image)

        # 2. 创建门掩码和对象掩码
        door_mask = np.zeros((height, width), dtype=np.uint8)
        door_positions = []  # 记录门的位置
        object_masks = {
            "bedroom": np.zeros((height, width), dtype=np.uint8),
            "living_room": np.zeros((height, width), dtype=np.uint8),
            "kitchen": np.zeros((height, width), dtype=np.uint8)
        }

        # 统计检测到的对象
        detection_stats = {}
        for (x1, y1, x2, y2, class_id) in detections:
            class_info = self.class_info.get(class_id)
            if not class_info:
                continue

            obj_name = class_info["name"]
            detection_stats[obj_name] = detection_stats.get(obj_name, 0) + 1

            if class_info["name"] == "door":
                cv2.rectangle(door_mask, (x1, y1), (x2, y2), 1, -1)
                door_positions.append((x1, y1, x2, y2))
            elif class_info["room"] in object_masks:
                room_type = class_info["room"]
                cv2.rectangle(object_masks[room_type], (x1, y1), (x2, y2), 1, -1)

        print(f"检测统计: {detection_stats}")
        print(f"门的数量: {len(door_positions)}")

        # 3. 创建门分界线 - 门作为房间之间的边界
        door_boundaries = cv2.dilate(door_mask, np.ones((5, 5), np.uint8), iterations=1)

        # 4. 创建组合边界：图像边缘 + 门分界线
        combined_boundaries = np.maximum(edge_map > 100, door_boundaries)

        # 5. 初始化分割结果
        segmentation = np.zeros((height, width), dtype=np.uint8)

        # 6. 为每个房间类型进行区域扩展，以门为边界
        room_areas = {}
        for idx, room_type in enumerate(["bedroom", "living_room", "kitchen"]):
            if np.sum(object_masks[room_type]) == 0:
                print(f"跳过 {room_type}: 未检测到相关家具")
                continue

            print(f"处理 {room_type}...")

            # 获取对象区域作为种子
            seeds = binary_fill_holes(object_masks[room_type]).astype(np.uint8)

            # 扩展房间区域，以门为边界
            room_region = self.expand_room_with_door_boundaries(
                seeds, combined_boundaries
            )

            # 移除太小的区域并添加到分割结果
            contours, _ = cv2.findContours(room_region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            total_area = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= self.min_room_area:
                    cv2.drawContours(segmentation, [contour], -1, idx + 1, -1)
                    total_area += area

            room_areas[room_type] = total_area
            print(f"{room_type} 总面积: {total_area} 像素")

        # 7. 处理未分配区域为unknown房间
        print("处理unknown区域...")

        # 创建未分配区域的掩码
        unassigned_mask = np.ones((height, width), dtype=np.uint8)
        unassigned_mask[segmentation > 0] = 0  # 排除已分配的房间
        unassigned_mask[door_boundaries > 0] = 0  # 排除门边界

        # 使用连通组件分析找到独立的未分配区域
        num_labels, labels = cv2.connectedComponents(unassigned_mask)

        unknown_count = 0
        for label_id in range(1, num_labels):  # 跳过背景(0)
            region_mask = (labels == label_id).astype(np.uint8)
            area = np.sum(region_mask)

            if area >= self.min_room_area:
                # 这是一个足够大的unknown区域
                unknown_count += 1
                # 使用值4表示unknown房间
                segmentation[region_mask == 1] = 4

        print(f"发现 {unknown_count} 个unknown房间区域")

        # 8. 标记门区域 (使用值5)
        segmentation[door_mask == 1] = 5

        # 9. 创建彩色可视化
        color_map = np.zeros((height, width, 3), dtype=np.uint8)

        # 已知房间类型
        room_mapping = {1: "bedroom", 2: "living_room", 3: "kitchen"}
        for room_id, room_name in room_mapping.items():
            color_map[segmentation == room_id] = self.room_colors[room_name]

        # unknown房间 (灰色)
        # color_map[segmentation == 4] = (255, 0, 255)

        # 门 (青色)
        color_map[segmentation == 5] = self.room_colors["door"]

        # 统计最终结果
        unique_ids, counts = np.unique(segmentation, return_counts=True)
        final_stats = {}
        for seg_id, count in zip(unique_ids, counts):
            if seg_id == 0:
                final_stats["unassigned"] = count
            elif seg_id in room_mapping:
                final_stats[room_mapping[seg_id]] = count
            elif seg_id == 4:
                final_stats["unknown"] = count
            elif seg_id == 5:
                final_stats["door"] = count

        print(f"最终分割统计: {final_stats}")

        return color_map, segmentation

    def process_image(self, image_path, label_path, output_path):
        """处理单张图像"""
        img = cv2.imread(image_path)
        if img is None:
            print(f"无法读取图像: {image_path}")
            return

        height, width = img.shape[:2]
        detections = self.load_yolo_labels(label_path, (width, height))

        # 创建分割掩码
        room_mask, segmentation = self.create_door_boundary_segmentation(img, detections)

        # 可视化叠加
        result = cv2.addWeighted(img, 0.7, room_mask, 0.3, 0)
        cv2.imwrite(output_path, result)

        # 保存纯分割掩码
        mask_path = output_path.replace('.png', '_mask.png')
        cv2.imwrite(mask_path, segmentation * 50)  # 乘以50使不同区域更明显

        print(f"处理完成: {image_path}")
        print(f"结果保存至: {output_path}")
        print(f"掩码保存至: {mask_path}")

        return segmentation

    def load_yolo_labels(self, label_path, img_size):
        """解析YOLO格式标签文件"""
        detections = []
        img_width, img_height = img_size
        
        if not os.path.exists(label_path):
            return detections
            
        with open(label_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) < 5:
                    continue
                    
                # 解析YOLO格式
                class_id = int(parts[0])
                x_center = float(parts[1]) * img_width
                y_center = float(parts[2]) * img_height
                width = float(parts[3]) * img_width
                height = float(parts[4]) * img_height
                
                # 计算边界框
                x1 = int(x_center - width/2)
                y1 = int(y_center - height/2)
                x2 = int(x_center + width/2)
                y2 = int(y_center + height/2)
                
                # 确保坐标在图像范围内
                x1 = max(0, min(x1, img_width-1))
                y1 = max(0, min(y1, img_height-1))
                x2 = max(0, min(x2, img_width-1))
                y2 = max(0, min(y2, img_height-1))
                
                detections.append((x1, y1, x2, y2, class_id))
                
        return detections


# 使用示例
if __name__ == "__main__":
    # 配置路径
    IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
    OUTPUT_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/room_masks"
    
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 创建分割器
    segmenter = ImprovedRoomSegmenter()
    
    # 处理所有图像
    for img_name in os.listdir(IMAGE_DIR):
        if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(IMAGE_DIR, img_name)
            label_name = os.path.splitext(img_name)[0] + '.txt'
            label_path = os.path.join(LABEL_DIR, label_name)
            output_path = os.path.join(OUTPUT_DIR, f"seg_{img_name}")
            
            segmenter.process_image(img_path, label_path, output_path)