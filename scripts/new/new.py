import cv2
import numpy as np
import os
from pathlib import Path

# ====== 参数配置 ======
IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/images/val"
LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/salm_0617/labels/val"

IMAGE_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"
LABEL_DIR = "/home/<USER>/panpan/code/ultralytics-main/datasets/test"

OUTPUT_DIR = "output"
AREA_THRESH = 300

class_names = {
    0: "bed_grounded",
    1: "bed_highleg",
    2: "sofa_grounded",
    3: "sofa_highleg",
    4: "door",
    5: "dining_table_set"
}

room_type_map = {
    0: "bedroom",
    1: "bedroom",
    2: "livingroom",
    3: "livingroom",
    5: "diningroom"
}

# 固定颜色映射 (BGR)
room_color_map = {
    "bedroom": (128, 128, 255),
    "livingroom": (128, 255, 128),
    "diningroom": (255, 128, 128),
    "unknow": (255, 192, 0)
}

def load_yolo_labels(label_file, img_w, img_h):
    boxes, classes = [], []
    with open(label_file, 'r') as f:
        for line in f:
            cls, cx, cy, w, h = map(float, line.strip().split())
            cls = int(cls)
            x1 = int((cx - w / 2) * img_w)
            y1 = int((cy - h / 2) * img_h)
            x2 = int((cx + w / 2) * img_w)
            y2 = int((cy + h / 2) * img_h)
            boxes.append([x1, y1, x2, y2])
            classes.append(cls)
    return boxes, classes

def process_image(image_path, label_path, save_dir):
    img_name = Path(image_path).stem
    image = cv2.imread(image_path)
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    h, w = gray.shape

    boxes, classes = load_yolo_labels(label_path, w, h)

    _, binary = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

    for (x1, y1, x2, y2), cls in zip(boxes, classes):
        if cls == 4:  # door
            cv2.rectangle(binary, (x1, y1), (x2, y2), 0, -1)
        if cls in [0, 2]:
            cv2.rectangle(binary, (x1, y1), (x2, y2), 255, -1)
    num_labels, labels_im = cv2.connectedComponents(binary)

    vis_image = image.copy()
    color_mask = np.zeros_like(image)
    font = cv2.FONT_HERSHEY_SIMPLEX
    room_idx = 0

    for i in range(1, num_labels):
        mask = (labels_im == i)
        area = np.sum(mask)
        if area < AREA_THRESH:
            continue

        present_classes = set()
        for (x1, y1, x2, y2), cls in zip(boxes, classes):
            if cls == 4: continue
            bx = (x1 + x2) // 2
            by = (y1 + y2) // 2
            # if 0 <= bx < w and 0 <= by < h and mask[by, bx]:
            if 0 <= bx < w and 0 <= by < h:
                present_classes.add(cls)

        if 5 in present_classes and (2 in present_classes or 3 in present_classes):
            room_type = "livingroom"
        else:
            room_type = "unknow"
            for cls in present_classes:
                if cls in room_type_map:
                    room_type = room_type_map[cls]
                    break

        color = room_color_map[room_type]
        vis_image[mask] = cv2.addWeighted(vis_image, 0.5, np.full_like(image, color), 0.7, 0)[mask]
        color_mask[mask] = color

        ys, xs = np.where(mask)
        cx, cy = int(np.mean(xs)), int(np.mean(ys))
        # cv2.putText(vis_image, f"{room_type}_{room_idx}", (cx - 40, cy), font, 0.5, (0, 0, 0), 2)
        cv2.putText(vis_image, f"{room_type}", (cx - 40, cy), font, 0.5, (0, 0, 0), 2)
        cv2.imwrite(f"{save_dir}/{room_type}_{room_idx}.png", (mask.astype(np.uint8) * 255))
        room_idx += 1

        # ===== 在 vis_image 上绘制 door 框 (粉紫色) =====
    for (x1, y1, x2, y2), cls in zip(boxes, classes):
        if cls == 4:
            cv2.rectangle(vis_image, (x1, y1), (x2, y2), (255, 0, 255), 2)

    # ============ 构建目标框图（不加类别文字） ============
    object_color_map = {
        "bed_grounded": (160, 160, 255),
        "bed_highleg": (100, 100, 255),
        "sofa_grounded": (160, 255, 160),
        "sofa_highleg": (100, 255, 100),
        "door": (255, 0, 255),
        "dining_table_set": (0, 128, 255)
    }

    boxed_image = image.copy()
    for (x1, y1, x2, y2), cls in zip(boxes, classes):
        label = class_names.get(cls, str(cls))
        color = object_color_map.get(label, (0, 255, 255))  # 默认黄色
        cv2.rectangle(boxed_image, (x1, y1), (x2, y2), color, 2)

    # ============ 拼接结果 ============
    vis_h, vis_w = vis_image.shape[:2]
    legend_height = 100  # 上方图例高度
    final_image = np.ones((vis_h + legend_height, vis_w * 2, 3), dtype=np.uint8) * 255
    final_image[legend_height:, :vis_w] = vis_image
    final_image[legend_height:, vis_w:] = boxed_image

        # ===== 添加中间分隔线 =====
    separator_x = vis_w  # 分隔线 x 位置
    cv2.line(final_image, (separator_x, legend_height), (separator_x, vis_h + legend_height), (255, 255, 255), 2)


    # ============ 添加图例 ============
    legend_font = 0.45
    y_offset_1 = 20
    y_offset_2 = 20
    y_offset_3 = 50
    y_offset_4 = 80
    x_step = 150

    # # 第一排：房间类型
    # for i, room in enumerate(["bedroom", "livingroom", "diningroom", "unknow"]):
    #     color = room_color_map[room]
    #     x = 10 + i * x_step
    #     cv2.rectangle(final_image, (x, y_offset_1 - 15), (x + 20, y_offset_1 + 5), color, -1)
    #     cv2.putText(final_image, room, (x + 25, y_offset_1 + 2), font, legend_font, (0, 0, 0), 1)

    # 第二排：目标类别
    for i, label in enumerate([
        "bed_grounded", "bed_highleg"
    ]):
        color = object_color_map[label]
        x = 10 + i * x_step
        cv2.rectangle(final_image, (x, y_offset_2 - 15), (x + 20, y_offset_2 + 5), color, -1)
        cv2.putText(final_image, label, (x + 25, y_offset_2 + 2), font, legend_font, (0, 0, 0), 1)

    # 第二排：目标类别
    for i, label in enumerate([
        "sofa_grounded", "sofa_highleg"
    ]):
        color = object_color_map[label]
        x = 10 + i * x_step
        cv2.rectangle(final_image, (x, y_offset_3 - 15), (x + 20, y_offset_3 + 5), color, -1)
        cv2.putText(final_image, label, (x + 25, y_offset_3 + 2), font, legend_font, (0, 0, 0), 1)

# 第二排：目标类别
    for i, label in enumerate([
        "door", "dining_table_set"
    ]):
        color = object_color_map[label]
        x = 10 + i * x_step
        cv2.rectangle(final_image, (x, y_offset_4 - 15), (x + 20, y_offset_4 + 5), color, -1)
        cv2.putText(final_image, label, (x + 25, y_offset_4 + 2), font, legend_font, (0, 0, 0), 1)


    # ============ 保存 ============
    cv2.imwrite(f"{save_dir}/overlay.png", vis_image)
    cv2.imwrite(f"{save_dir}/mask_color.png", color_mask)
    cv2.imwrite(f"{save_dir}/boxed.png", boxed_image)
    cv2.imwrite(f"{save_dir}/final_result.png", final_image)
    print(f"✅ Processed {img_name}, Saved to {save_dir}")

def batch_process():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    for img_file in sorted(os.listdir(IMAGE_DIR)):
        if not img_file.lower().endswith((".jpg", ".png")):
            continue
        img_path = os.path.join(IMAGE_DIR, img_file)
        label_path = os.path.join(LABEL_DIR, Path(img_file).with_suffix(".txt"))
        if not os.path.exists(label_path):
            print(f"⚠️ Label not found for {img_file}")
            continue
        save_dir = os.path.join(OUTPUT_DIR, Path(img_file).stem)
        os.makedirs(save_dir, exist_ok=True)
        process_image(img_path, label_path, save_dir)

if __name__ == "__main__":
    batch_process()
