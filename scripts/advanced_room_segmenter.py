#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级房间分割器
基于目标检测结果推断完整房间区域的分割

作者: AI Assistant
日期: 2025-06-18
"""

import os
import cv2
import numpy as np
from scipy.ndimage import binary_fill_holes
from typing import List, Tuple, Dict, Optional
import json

class AdvancedRoomSegmenter:
    """高级房间分割器 - 基于家具检测结果推断完整房间区域"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化分割器
        
        Args:
            config: 配置字典，包含各种参数设置
        """
        # 默认配置
        default_config = {
            "edge_threshold1": 50,
            "edge_threshold2": 150,
            "door_barrier_width": 15,
            "expansion_iterations": 25,
            "min_room_area": 1000,
            "morphology_kernel_size": 5,
            "gaussian_blur_size": 5
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 类别定义 - 家具到房间的映射
        self.class_info = {
            0: {"name": "bed_grounded", "room": "bedroom", "priority": 1},
            1: {"name": "bed_highleg", "room": "bedroom", "priority": 1},
            2: {"name": "sofa_grounded", "room": "living_room", "priority": 1},
            3: {"name": "sofa_highleg", "room": "living_room", "priority": 1},
            4: {"name": "door", "room": None, "priority": 0},
            5: {"name": "dining_table_set", "room": "kitchen", "priority": 1}
        }
        
        # 房间颜色映射 (BGR格式)
        self.room_colors = {
            "bedroom": (0, 0, 255),      # 红色
            "living_room": (0, 255, 0),  # 绿色
            "kitchen": (255, 0, 0),      # 蓝色
            "door": (255, 255, 0),       # 青色
            "unknown": (128, 128, 128)   # 灰色
        }
        
        # 房间ID映射
        self.room_ids = {
            "bedroom": 1,
            "living_room": 2,
            "kitchen": 3,
            "unknown": 4,
            "door": 5,
            "unassigned": 0
        }

    def load_yolo_labels(self, label_path: str, img_size: Tuple[int, int]) -> List[Tuple]:
        """
        解析YOLO格式标签文件
        
        Args:
            label_path: 标签文件路径
            img_size: 图像尺寸 (width, height)
            
        Returns:
            检测结果列表 [(x1, y1, x2, y2, class_id), ...]
        """
        detections = []
        img_width, img_height = img_size
        
        if not os.path.exists(label_path):
            print(f"警告: 标签文件不存在 {label_path}")
            return detections
            
        with open(label_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split()
                if len(parts) < 5:
                    print(f"警告: 第{line_num}行格式错误，跳过")
                    continue
                    
                try:
                    # 解析YOLO格式 (class_id, x_center, y_center, width, height)
                    class_id = int(parts[0])
                    x_center = float(parts[1]) * img_width
                    y_center = float(parts[2]) * img_height
                    width = float(parts[3]) * img_width
                    height = float(parts[4]) * img_height
                    
                    # 计算边界框坐标
                    x1 = int(x_center - width/2)
                    y1 = int(y_center - height/2)
                    x2 = int(x_center + width/2)
                    y2 = int(y_center + height/2)
                    
                    # 确保坐标在图像范围内
                    x1 = max(0, min(x1, img_width-1))
                    y1 = max(0, min(y1, img_height-1))
                    x2 = max(0, min(x2, img_width-1))
                    y2 = max(0, min(y2, img_height-1))
                    
                    detections.append((x1, y1, x2, y2, class_id))
                    
                except (ValueError, IndexError) as e:
                    print(f"警告: 第{line_num}行解析错误: {e}")
                    continue
                    
        print(f"成功加载 {len(detections)} 个检测结果")
        return detections

    def detect_room_boundaries(self, image: np.ndarray) -> np.ndarray:
        """
        检测房间边界
        
        Args:
            image: 输入图像
            
        Returns:
            边界掩码
        """
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊减少噪声
        blur_size = self.config["gaussian_blur_size"]
        gray = cv2.GaussianBlur(gray, (blur_size, blur_size), 0)
        
        # 使用Canny检测边缘
        edges = cv2.Canny(
            gray, 
            self.config["edge_threshold1"], 
            self.config["edge_threshold2"]
        )
        
        # 形态学操作连接断裂的边缘
        kernel_size = self.config["morphology_kernel_size"]
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        return edges

    def expand_region_to_doors(self, seed_mask: np.ndarray,
                             boundary_mask: np.ndarray,
                             door_mask: np.ndarray,
                             iterations: int = 50) -> np.ndarray:
        """
        扩展区域直到遇到门或图像边界

        Args:
            seed_mask: 种子区域掩码
            boundary_mask: 图像边界掩码
            door_mask: 门掩码
            iterations: 最大迭代次数

        Returns:
            扩展后的区域掩码
        """
        current_mask = seed_mask.copy()
        kernel = np.ones((3, 3), np.uint8)

        # 创建停止条件：图像边界但不包括门
        stop_mask = boundary_mask.copy()
        stop_mask[door_mask > 0] = 0  # 允许扩展到门区域

        for i in range(iterations):
            # 膨胀当前区域
            expanded = cv2.dilate(current_mask, kernel, iterations=1)

            # 移除图像边界区域，但保留门区域
            expanded[stop_mask > 0] = 0

            # 如果没有新的扩展，停止
            if np.array_equal(expanded, current_mask):
                print(f"区域扩展在第{i+1}次迭代后收敛")
                break

            current_mask = expanded

        return current_mask

    def expand_region_iteratively(self, seed_mask: np.ndarray,
                                boundary_mask: np.ndarray,
                                iterations: int = 20) -> np.ndarray:
        """
        迭代扩展区域直到遇到边界 (保持向后兼容)
        """
        return self.expand_region_to_doors(seed_mask, boundary_mask,
                                         np.zeros_like(seed_mask), iterations)

    def create_room_segmentation(self, image: np.ndarray, 
                               detections: List[Tuple]) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建房间分割
        
        Args:
            image: 输入图像
            detections: 检测结果列表
            
        Returns:
            (彩色可视化图, 分割掩码)
        """
        height, width = image.shape[:2]
        print(f"处理图像尺寸: {width}x{height}")
        
        # 1. 检测房间边界
        boundary_map = self.detect_room_boundaries(image)
        
        # 2. 创建门掩码和对象掩码
        door_mask = np.zeros((height, width), dtype=np.uint8)
        object_masks = {
            "bedroom": np.zeros((height, width), dtype=np.uint8),
            "living_room": np.zeros((height, width), dtype=np.uint8),
            "kitchen": np.zeros((height, width), dtype=np.uint8)
        }
        
        # 统计检测到的对象
        detection_stats = {}
        for (x1, y1, x2, y2, class_id) in detections:
            class_info = self.class_info.get(class_id)
            if not class_info:
                continue
                
            obj_name = class_info["name"]
            detection_stats[obj_name] = detection_stats.get(obj_name, 0) + 1
                
            if class_info["name"] == "door":
                cv2.rectangle(door_mask, (x1, y1), (x2, y2), 1, -1)
            elif class_info["room"] in object_masks:
                room_type = class_info["room"]
                cv2.rectangle(object_masks[room_type], (x1, y1), (x2, y2), 1, -1)
        
        print(f"检测统计: {detection_stats}")
        
        # 3. 创建门区域 (不扩展，保持原始大小)
        # 门应该被包含在房间内，而不是作为边界

        # 4. 只使用图像边缘作为真正的边界
        combined_boundaries = boundary_map.copy()
        
        # 5. 初始化分割结果
        segmentation = np.zeros((height, width), dtype=np.uint8)
        
        # 6. 为每个已知房间类型进行区域扩展
        room_areas = {}
        for room_type in ["bedroom", "living_room", "kitchen"]:
            if np.sum(object_masks[room_type]) == 0:
                print(f"跳过 {room_type}: 未检测到相关家具")
                continue

            print(f"处理 {room_type}...")

            # 获取对象区域作为种子
            seeds = binary_fill_holes(object_masks[room_type]).astype(np.uint8)

            # 使用新的扩展方法，允许扩展到门区域
            room_region = self.expand_region_to_doors(
                seeds, combined_boundaries, door_mask, self.config["expansion_iterations"]
            )

            # 移除太小的区域并添加到分割结果
            contours, _ = cv2.findContours(room_region, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            total_area = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area >= self.config["min_room_area"]:
                    room_id = self.room_ids[room_type]
                    cv2.drawContours(segmentation, [contour], -1, room_id, -1)
                    total_area += area

            room_areas[room_type] = total_area
            print(f"{room_type} 总面积: {total_area} 像素")

        # 7. 处理unknown区域 - 找到未被分配的连通区域
        print("处理unknown区域...")

        # 创建未分配区域的掩码 (只排除已分配的房间)
        unassigned_mask = np.ones((height, width), dtype=np.uint8)
        unassigned_mask[segmentation > 0] = 0  # 排除已分配的房间
        # 不排除门区域，让unknown区域也可以包含门

        # 使用连通组件分析找到独立的未分配区域
        num_labels, labels = cv2.connectedComponents(unassigned_mask)

        unknown_count = 0
        for label_id in range(1, num_labels):  # 跳过背景(0)
            region_mask = (labels == label_id).astype(np.uint8)
            area = np.sum(region_mask)

            if area >= self.config["min_room_area"]:
                # 这是一个足够大的unknown区域
                unknown_count += 1
                unknown_id = self.room_ids["unknown"]
                segmentation[region_mask == 1] = unknown_id

        print(f"发现 {unknown_count} 个unknown房间区域")
        room_areas["unknown"] = np.sum(segmentation == self.room_ids["unknown"])

        # 8. 门区域已经被包含在各个房间中，不需要单独标记
        # 这样门就成为了房间的一部分，而不是独立的区域
        
        # 9. 创建彩色可视化
        color_map = self.create_color_visualization(segmentation)
        
        print(f"分割完成，房间面积统计: {room_areas}")
        return color_map, segmentation

    def create_color_visualization(self, segmentation: np.ndarray) -> np.ndarray:
        """创建彩色可视化图"""
        height, width = segmentation.shape
        color_map = np.zeros((height, width, 3), dtype=np.uint8)

        for room_name, room_id in self.room_ids.items():
            if room_id == 0:  # unassigned
                continue
            mask = segmentation == room_id
            if room_name in self.room_colors:
                color_map[mask] = self.room_colors[room_name]

        # 处理未分配区域 (背景)
        unassigned_mask = segmentation == 0
        color_map[unassigned_mask] = self.room_colors["unknown"]

        return color_map

    def save_training_data(self, segmentation: np.ndarray, output_path: str,
                          format_type: str = "mask") -> str:
        """
        保存用于训练的分割数据

        Args:
            segmentation: 分割掩码
            output_path: 输出路径
            format_type: 数据格式 ("mask", "coco", "pascal")

        Returns:
            保存的文件路径
        """
        if format_type == "mask":
            # 保存为灰度掩码图像，每个像素值代表类别ID
            cv2.imwrite(output_path, segmentation.astype(np.uint8))
            return output_path

        elif format_type == "coco":
            # 保存为COCO格式的分割数据
            import json

            # 为每个房间类型创建分割多边形
            annotations = []
            for room_name, room_id in self.room_ids.items():
                if room_id == 0:  # 跳过unassigned
                    continue

                mask = (segmentation == room_id).astype(np.uint8)
                if np.sum(mask) == 0:
                    continue

                # 找到轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                for contour in contours:
                    if cv2.contourArea(contour) < 100:  # 过滤小轮廓
                        continue

                    # 简化轮廓
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)

                    # 转换为COCO格式
                    segmentation_points = []
                    for point in approx:
                        segmentation_points.extend([int(point[0][0]), int(point[0][1])])

                    if len(segmentation_points) >= 6:  # 至少3个点
                        annotations.append({
                            "category_id": room_id,
                            "category_name": room_name,
                            "segmentation": [segmentation_points],
                            "area": float(cv2.contourArea(contour)),
                            "bbox": [int(x) for x in cv2.boundingRect(contour)]
                        })

            # 保存JSON文件
            json_path = output_path.replace('.png', '.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "annotations": annotations,
                    "categories": [
                        {"id": room_id, "name": room_name}
                        for room_name, room_id in self.room_ids.items()
                        if room_id > 0
                    ]
                }, f, indent=2, ensure_ascii=False)

            return json_path

        else:
            raise ValueError(f"不支持的格式类型: {format_type}")

    def generate_training_dataset(self, input_dir: str, output_dir: str,
                                format_type: str = "mask") -> Dict:
        """
        批量生成训练数据集

        Args:
            input_dir: 输入目录 (包含图像和标签)
            output_dir: 输出目录
            format_type: 数据格式

        Returns:
            数据集统计信息
        """
        from pathlib import Path

        input_path = Path(input_dir)
        output_path = Path(output_dir)

        # 创建输出目录结构
        images_dir = output_path / "images"
        masks_dir = output_path / "masks"
        annotations_dir = output_path / "annotations"

        images_dir.mkdir(parents=True, exist_ok=True)
        masks_dir.mkdir(parents=True, exist_ok=True)
        if format_type == "coco":
            annotations_dir.mkdir(parents=True, exist_ok=True)

        # 查找所有图像文件
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))

        dataset_stats = {
            "total_images": 0,
            "successful_images": 0,
            "room_statistics": {},
            "files": []
        }

        print(f"开始生成训练数据集...")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        print(f"数据格式: {format_type}")

        for image_file in image_files:
            dataset_stats["total_images"] += 1

            # 查找对应的标签文件
            label_file = image_file.with_suffix('.txt')
            if not label_file.exists():
                print(f"⚠️  跳过 {image_file.name}: 未找到标签文件")
                continue

            try:
                # 读取图像
                img = cv2.imread(str(image_file))
                if img is None:
                    print(f"❌ 无法读取图像: {image_file}")
                    continue

                height, width = img.shape[:2]
                detections = self.load_yolo_labels(str(label_file), (width, height))

                # 创建分割
                _, segmentation = self.create_room_segmentation(img, detections)

                # 复制原始图像
                base_name = image_file.stem
                img_output_path = images_dir / f"{base_name}.png"
                cv2.imwrite(str(img_output_path), img)

                # 保存分割掩码
                mask_output_path = masks_dir / f"{base_name}.png"
                training_data_path = self.save_training_data(
                    segmentation, str(mask_output_path), format_type
                )

                # 统计房间信息
                unique_ids, counts = np.unique(segmentation, return_counts=True)
                image_room_stats = {}
                for room_id, count in zip(unique_ids, counts):
                    for room_name, rid in self.room_ids.items():
                        if rid == room_id:
                            image_room_stats[room_name] = int(count)
                            if room_name not in dataset_stats["room_statistics"]:
                                dataset_stats["room_statistics"][room_name] = 0
                            dataset_stats["room_statistics"][room_name] += int(count)
                            break

                dataset_stats["files"].append({
                    "image": str(img_output_path),
                    "mask": str(mask_output_path),
                    "annotation": training_data_path if format_type == "coco" else None,
                    "room_stats": image_room_stats
                })

                dataset_stats["successful_images"] += 1
                print(f"✅ 处理完成: {base_name}")

            except Exception as e:
                print(f"❌ 处理失败 {image_file.name}: {e}")
                continue

        # 保存数据集信息
        dataset_info_path = output_path / "dataset_info.json"
        with open(dataset_info_path, 'w', encoding='utf-8') as f:
            json.dump(dataset_stats, f, indent=2, ensure_ascii=False)

        print(f"\n📊 数据集生成完成!")
        print(f"- 总图像数: {dataset_stats['total_images']}")
        print(f"- 成功处理: {dataset_stats['successful_images']}")
        print(f"- 数据集信息: {dataset_info_path}")

        return dataset_stats

    def process_image(self, image_path: str, label_path: str, 
                     output_dir: str) -> Dict:
        """
        处理单张图像
        
        Args:
            image_path: 图像路径
            label_path: 标签路径
            output_dir: 输出目录
            
        Returns:
            处理结果字典
        """
        print(f"\n开始处理: {image_path}")
        
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        height, width = img.shape[:2]
        
        # 加载检测结果
        detections = self.load_yolo_labels(label_path, (width, height))
        
        # 创建分割
        room_mask, segmentation = self.create_room_segmentation(img, detections)
        
        # 保存结果
        os.makedirs(output_dir, exist_ok=True)
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        
        # 保存可视化结果
        result = cv2.addWeighted(img, 0.7, room_mask, 0.3, 0)
        result_path = os.path.join(output_dir, f"{base_name}_segmented.png")
        cv2.imwrite(result_path, result)
        
        # 保存纯分割掩码
        mask_path = os.path.join(output_dir, f"{base_name}_mask.png")
        cv2.imwrite(mask_path, segmentation * 50)  # 乘以50使不同区域更明显
        
        # 保存彩色掩码
        color_mask_path = os.path.join(output_dir, f"{base_name}_color_mask.png")
        cv2.imwrite(color_mask_path, room_mask)
        
        # 统计信息
        unique_ids, counts = np.unique(segmentation, return_counts=True)
        room_stats = {}
        for room_id, count in zip(unique_ids, counts):
            for room_name, rid in self.room_ids.items():
                if rid == room_id:
                    room_stats[room_name] = {
                        "pixel_count": int(count),
                        "percentage": float(count / (width * height) * 100)
                    }
                    break
        
        result_info = {
            "image_path": image_path,
            "output_paths": {
                "segmented": result_path,
                "mask": mask_path,
                "color_mask": color_mask_path
            },
            "room_statistics": room_stats,
            "image_size": {"width": width, "height": height},
            "detection_count": len(detections)
        }
        
        print(f"处理完成!")
        print(f"- 分割结果: {result_path}")
        print(f"- 掩码文件: {mask_path}")
        print(f"- 彩色掩码: {color_mask_path}")
        
        return result_info

if __name__ == "__main__":
    # 使用示例
    segmenter = AdvancedRoomSegmenter()
    
    # 处理测试图像
    image_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test/9176a0d5-shunzao_10499.png"
    label_path = "/home/<USER>/panpan/code/ultralytics-main/datasets/test/9176a0d5-shunzao_10499.txt"
    output_dir = "/home/<USER>/panpan/code/ultralytics-main/datasets/room_masks"
    
    try:
        result = segmenter.process_image(image_path, label_path, output_dir)
        print(f"\n处理结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"处理失败: {e}")
